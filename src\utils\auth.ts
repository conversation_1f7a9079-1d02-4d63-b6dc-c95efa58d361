export async function getToken() {
  return await window.ipc<PERSON>enderer?.invoke('get-token') || null;
}

export async function setToken(token: string) {
  return await window.ipcRenderer?.invoke('set-token', token);
}

export async function removeToken() {
  return await window.ipcRenderer?.invoke('remove-token');
}

export async function getPermissions() {
  return await window.ipcRenderer?.invoke('get-permissions');
}

export async function setPermissions(permissions: string[]) {
  return await window.ipcRenderer?.invoke('set-permissions', permissions);
}

export async function removePermissions() {
  return await window.ipcRenderer?.invoke('remove-permissions');
}
