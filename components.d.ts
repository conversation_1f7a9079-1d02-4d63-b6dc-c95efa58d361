// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    MembershipDialog: typeof import('./src/components/MembershipDialog/index.vue')['default']
    Menu: typeof import('./src/components/Menu/index.vue')['default']
    PayDialog: typeof import('./src/components/PayDialog/index.vue')['default']
    PlatFormIcon: typeof import('./src/components/PlatFormIcon/index.vue')['default']
    RechargeDialog: typeof import('./src/components/RechargeDialog/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/SvgIcon/index.vue')['default']
    SysTitleBar: typeof import('./src/components/SysTitleBar/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
