interface IUserMembership {
    isMember: boolean;
    membershipType: null | string
    expiresAt: null | string
    daysRemaining: number
    permissions: string[]
}

interface IUserInfo {
    id: string
    isActive: boolean
    email: string
    name: string
    pointsBalance: number
    membership: IUserMembership
}

type IPlatformKey = 'whatsapp' | 'telegram' | 'line';

type IPlatformState = 'normal' | 'permission' | 'unlink';

interface IPlatform {
    name: string;
    key: IPlatformKey;
    state: IPlatformState;
    icon: string;
    activeIcon?: string;
    darkIcon?: string;
    darkActiveIcon?: string;
    color: string;
    activeColor: string;
}

export interface IAccountInfo {
    avatar?: string;
    accountId?: string;
    nickname?: string;
    isLogined?: boolean;
    unreadCount?: number;
}

export interface IChatWindowStatus {
    isLoading: boolean;
    isLoadFail: boolean;
    isLoaded: boolean;
    isShow: boolean;
    isCreated: boolean;
}

export type IChatWindowInfo = IChatWindowStatus & IAccountInfo;

interface Window {
    ipcRenderer?: any;
}