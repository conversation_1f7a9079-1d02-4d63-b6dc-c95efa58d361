<template>
    <el-dialog v-model="dialogVisible" title="购买会员" width="800" @close="handleClose" :close-on-click-modal="false">
        <el-table :data="products" class="w-full" v-loading="loading" highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column prop="name" label="套餐"></el-table-column>
            <el-table-column prop="description" label="套餐详情"></el-table-column>
            <el-table-column prop="price" label="时长(天)" width="120">
                <template #default="scope">
                    {{ `${scope.row.duration}天` }}
                </template>
            </el-table-column>
            <el-table-column prop="price" label="价格(元)" width="120">
                <template #default="scope">
                    {{ (scope.row.price / 100).toFixed(2) }}
                </template>
            </el-table-column>
        </el-table>

        <div class="mt-4">
            <el-radio-group v-model="paymentMethod">
                <el-radio value="alipay">支付宝支付</el-radio>
                <el-radio value="wxpay" :disabled="true">微信支付</el-radio>
            </el-radio-group>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handlePay()">去支付</el-button>
            </span>
        </template>
        <PayDialog ref="payDialogRef"></PayDialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import PayDialog from '@/components/PayDialog/index.vue';
import { createMembershipOrderAPI, getMembershipProductsAPI, MembershipProduct, payMembershipOrderAPI } from '@/api/membership';

const dialogVisible = defineModel<boolean>({ required: true })

const products = ref<MembershipProduct[]>([])
const loading = ref<boolean>(false)
const paymentMethod = ref<'alipay' | 'wxpay'>('alipay')
const currentRow = ref<MembershipProduct | undefined>(undefined)
const payDialogRef = ref<InstanceType<typeof PayDialog>>()

// 获取积分商品列表
const fetchProducts = async () => {
    try {
        loading.value = true
        const res = await getMembershipProductsAPI()
        products.value = res.data || []
    } catch (error) {
        ElMessage.error('获取套餐列表失败')
    } finally {
        loading.value = false
    }
}


const handleCurrentChange = (val: MembershipProduct | undefined) => {
    currentRow.value = val
}

// 处理支付
const handlePay = async () => {
    try {
        const product = currentRow.value;
        if (!product) {
            ElMessage.error('请选择会员套餐')
            return
        }
        // 创建订单
        const orderRes = await createMembershipOrderAPI({ productId: product.id })
        const order = orderRes.data

        // 创建支付链接
        const paymentRes = await payMembershipOrderAPI({
            orderNo: order.orderNo,
            paymentMethod: 'alipay'
        })

        payDialogRef.value?.open(order.orderNo, paymentRes.data.img)

    } catch (error) {
        console.error('创建订单失败')
    }
}

const handleClose = () => {
    // 清理操作
}

onMounted(() => {
    fetchProducts()
})
</script>
