import { Context } from 'koa';
import { ClientService } from '../../services/client.service';
import { EmailService } from '../../services/email.service';
import { CaptchaService } from '../../services/captcha.service';
import { ClientMembershipService } from '../../services/client-membership.service';

interface RequestBody {
  email?: string;
  password?: string;
  confirmPassword?: string;
  name?: string;
  phone?: string;
  token?: string;
  oldPassword?: string;
  newPassword?: string;
}

export class ClientAuthController {
  private clientService: ClientService;
  private emailService: EmailService;
  private captchaService: CaptchaService;
  private clientMembershipService: ClientMembershipService;

  constructor() {
    this.clientService = new ClientService();
    this.emailService = new EmailService();
    this.captchaService = new CaptchaService();
    this.clientMembershipService = new ClientMembershipService();
  }

  /**
   * 获取用户会员信息的辅助方法
   */
  private async getMembershipInfo(userId: string) {
    const membership = await this.clientMembershipService.getCurrentMembership(userId);

    // 计算会员剩余时长（天数）
    let membershipInfo = {
      isMember: false,
      membershipType: null as string | null,
      expiresAt: null as Date | null,
      daysRemaining: 0,
      permissions: [] as string[],
    };

    if (membership && membership.status === 'active' && membership.endTime > new Date()) {
      const now = new Date();
      const daysRemaining = Math.ceil(
        (membership.endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
      );

      // 获取会员权限列表
      const permissions =
        membership.membershipProduct?.permissions?.map((mp) => mp.permission.key) || [];

      membershipInfo = {
        isMember: true,
        membershipType: membership.membershipProduct?.name || null,
        expiresAt: membership.endTime,
        daysRemaining: Math.max(0, daysRemaining),
        permissions,
      };
    }

    return membershipInfo;
  }

  /**
   * 获取验证码
   */
  getCaptcha = async (ctx: Context): Promise<void> => {
    try {
      const { id, dataUrl } = await this.captchaService.generateCaptcha();
      ctx.body = {
        code: 200,
        data: {
          id,
          dataUrl,
        },
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取验证码失败',
      };
    }
  };

  /**
   * 发送注册验证码
   */
  sendRegisterCode = async (ctx: Context): Promise<void> => {
    const { email } = ctx.request.body as { email: string };

    if (!email) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '邮箱是必填项',
      };
      return;
    }

    try {
      const success = await this.emailService.sendVerificationCode(email);
      if (success) {
        ctx.body = {
          code: 200,
          message: '验证码已发送到您的邮箱',
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          code: 500,
          message: '发送验证码失败',
        };
      }
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '发送验证码失败',
      };
    }
  };

  /**
   * 客户端用户注册
   */
  register = async (ctx: Context): Promise<void> => {
    const { email, password, name, phone, verificationCode } = ctx.request.body as {
      email: string;
      password: string;
      name?: string;
      phone?: string;
      verificationCode: string;
    };

    if (!email || !password || !verificationCode) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '邮箱、密码和验证码是必填项',
      };
      return;
    }

    try {
      // 验证邮箱验证码
      const isCodeValid = await this.emailService.verifyCode(email, verificationCode);
      if (!isCodeValid) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '验证码错误或已过期',
        };
        return;
      }

      const result = await this.clientService.register({
        email,
        password,
        name: name || `User${Math.floor(100000 + Math.random() * 900000)}`,
        phone: phone || '',
      });

      // 获取会员状态信息
      const membershipInfo = await this.getMembershipInfo(result.client.id);

      // 过滤敏感字段
      const { client } = result;
      const safeClient = {
        id: client.id,
        name: client.name,
        email: client.email,
        createdAt: client.createdAt,
        membership: membershipInfo,
      };

      ctx.body = {
        code: 200,
        message: '注册成功',
        data: {
          token: result.token,
          user: safeClient,
        },
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '注册失败',
      };
    }
  };

  /**
   * 客户端用户登录
   */
  login = async (ctx: Context): Promise<void> => {
    const { email, password, captchaId, captchaAnswer } = ctx.request.body as {
      email: string;
      password: string;
      captchaId: string;
      captchaAnswer: string;
    };

    if (!email || !password || !captchaId || !captchaAnswer) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '邮箱、密码和验证码是必填项',
      };
      return;
    }

    try {
      // 验证验证码
      const isCaptchaValid = await this.captchaService.verifyCaptcha(captchaId, captchaAnswer);
      if (!isCaptchaValid) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '验证码错误或已过期',
        };
        return;
      }

      const result = await this.clientService.login({
        email,
        password,
      });

      // 获取会员状态信息
      const membershipInfo = await this.getMembershipInfo(result.client.id);

      // 过滤敏感字段
      const { client } = result;
      const safeClient = {
        id: client.id,
        name: client.name,
        email: client.email,
        createdAt: client.createdAt,
        membership: membershipInfo,
      };

      ctx.body = {
        code: 200,
        message: '登录成功',
        data: {
          token: result.token,
          user: safeClient,
        },
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '登录失败',
      };
    }
  };

  /**
   * 获取用户个人资料
   */
  getProfile = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const client = await this.clientService.findOne(userId);

      if (!client) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '用户不存在',
        };
        return;
      }

      // 获取会员状态信息
      const membershipInfo = await this.getMembershipInfo(userId);

      // 过滤敏感字段
      const safeClient = {
        id: client.id,
        name: client.name,
        email: client.email,
        phone: client.phone,
        tenantId: client.tenantId,
        isActive: client.isActive,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt,
        pointsBalance: client.pointsBalance,
        membership: membershipInfo,
      };

      ctx.body = {
        code: 200,
        data: safeClient,
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '获取个人资料失败',
      };
    }
  };

  /**
   * 更新用户个人资料
   */
  updateProfile = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const { name, phone } = ctx.request.body as RequestBody;

      const updateData: Record<string, any> = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;

      if (Object.keys(updateData).length === 0) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '没有提供要更新的数据',
        };
        return;
      }

      const success = await this.clientService.update(userId, updateData);

      if (!success) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '用户不存在',
        };
        return;
      }

      const updatedClient = await this.clientService.findOne(userId);

      // 检查更新后的用户是否存在
      if (!updatedClient) {
        ctx.status = 404;
        ctx.body = {
          code: 404,
          message: '获取更新后的用户信息失败',
        };
        return;
      }

      // 获取会员状态信息
      const membershipInfo = await this.getMembershipInfo(userId);

      // 过滤敏感字段
      const safeClient = {
        id: updatedClient.id,
        name: updatedClient.name,
        email: updatedClient.email,
        phone: updatedClient.phone,
        tenantId: updatedClient.tenantId,
        isActive: updatedClient.isActive,
        createdAt: updatedClient.createdAt,
        updatedAt: updatedClient.updatedAt,
        pointsBalance: updatedClient.pointsBalance,
        membership: membershipInfo,
      };

      ctx.body = {
        code: 200,
        message: '个人资料更新成功',
        data: safeClient,
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: error instanceof Error ? error.message : '更新个人资料失败',
      };
    }
  };

  /**
   * 修改密码
   */
  changePassword = async (ctx: Context): Promise<void> => {
    try {
      const userId = ctx.state.user.id;
      const { oldPassword, newPassword, confirmPassword } = ctx.request.body as RequestBody;

      if (!oldPassword || !newPassword || !confirmPassword) {
        ctx.status = 400;
        ctx.body = {
          code: 400,
          message: '旧密码、新密码和确认密码都是必填项',
        };
        return;
      }

      await this.clientService.changePassword(userId, oldPassword, newPassword, confirmPassword);

      ctx.body = {
        code: 200,
        message: '密码修改成功',
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '密码修改失败',
      };
    }
  };

  /**
   * 忘记密码
   */
  forgotPassword = async (ctx: Context): Promise<void> => {
    const { email } = ctx.request.body as RequestBody;

    if (!email) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '邮箱是必填项',
      };
      return;
    }

    try {
      await this.clientService.forgotPassword(email);

      ctx.body = {
        code: 200,
        message: '重置密码链接已发送至您的邮箱',
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '发送重置密码邮件失败',
      };
    }
  };

  /**
   * 重置密码
   */
  resetPassword = async (ctx: Context): Promise<void> => {
    const { token, password, confirmPassword } = ctx.request.body as RequestBody;

    if (!token || !password || !confirmPassword) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: '令牌、新密码和确认密码都是必填项',
      };
      return;
    }

    try {
      await this.clientService.resetPassword(token, password, confirmPassword);

      ctx.body = {
        code: 200,
        message: '密码重置成功',
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        code: 400,
        message: error instanceof Error ? error.message : '密码重置失败',
      };
    }
  };
}
