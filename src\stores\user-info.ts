// Pinia Store
import { defineStore } from 'pinia'
import { person } from '../api/user'
import { $message } from '../utils/message'
import { IUserInfo, IUserMembership } from '@/types';
import { getTranslationRouteListAPI } from '@/api/translation-route';
import { IEventType } from '@/constants/enum';
import { setPermissions } from '@/utils/auth';

interface IState {
    userInfo: null | IUserInfo;
    membership: null | IUserMembership;
    permissions: string[]
};

export const useUserInfoStore = defineStore('user/info', {
    // 转换为函数
    state: (): IState => ({
        userInfo: null,
        membership: null,
        permissions: []
    }),
    getters: {

    },
    actions: {
        getUserInfo(): Promise<IUserInfo | null> {
            return person().then((data) => {
                this.userInfo = data.data;
                this.membership = data.data.membership;
                this.permissions = data.data.membership.permissions || [];
                this.getRoutes()
                setPermissions(this.permissions)
                return this.userInfo;
            }).catch(err => {
                $message.error('获取用户信息失败');
                return null;
            });
        },
        getRoutes() {
            return getTranslationRouteListAPI().then(res => {
                window.ipcRenderer?.invoke(IEventType.SetTranslateRoutes, res.data || []);
                return res.data;
            });
        }
    },
})